import * as multisig from '@sqds/multisig'
import { PublicKey, SystemProgram } from '@solana/web3.js'
import TOS from '../tos.json' assert {type:'json'}
import { MULTISIG_ADDRESS, MULTISIG_PUBKEY, SQUADS_V4_PUBKEY, SPL_TOKEN_PUBKEY } from './constants.js'
import { connection, validateParams, handleError, getTokenAccounts, getPrice, sendTransaction, confirmTransactionWithTimeout, parsePermissions, calculateAssetWeights, parseSystemTransfer, parseSplTokenTransfer } from './utils.js'

export const getBlockhash = async ctx => {
  try {
    const { blockhash } = await connection.getLatestBlockhash()
    ctx.body = { blockhash }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const getBalance = async ctx => {
  try {
    const error = validateParams(ctx.request.body, ['publicKey'])
    if (error) return handleError(ctx, error)

    const { publicKey } = ctx.request.body
    const balance = await connection.getBalance(new PublicKey(publicKey))
    ctx.body = { balance }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const getTos = async ctx => {
  try {
    ctx.body = TOS
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const getMultisigs = async ctx => {
  try {
    // 多签账户信息
    const { threshold, transactionIndex, members } = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_PUBKEY)

    // 金库信息
    const [vaultPda] = multisig.getVaultPda({ multisigPda: MULTISIG_PUBKEY, index: 0, programId: SQUADS_V4_PUBKEY})
    const vaultBalance = await connection.getBalance(vaultPda)

    // 资产列表
    const assets = []
    const solPrice = await getPrice('SOL')

    // 资产列表 - SOL 资产
    if (vaultBalance > 0) {
      assets.push({
        symbol: 'SOL',
        address: vaultPda.toBase58(),
        balance: vaultBalance / 1e9,
        price: solPrice,
        value: (vaultBalance / 1e9) * solPrice
      })
    }

    // 资产列表 - Token 资产
    (await getTokenAccounts(vaultPda)).forEach(({ name, address, mint, decimals, uiAmount, price, value }) => assets.push({
      symbol: name,
      address,
      mint, // 保留 mint 地址，Token 转账需要
      decimals, // 保留 decimals，Token 转账需要
      balance: uiAmount,
      price,
      value
    }))

    ctx.body = {
      multisigAccount: MULTISIG_ADDRESS,
      threshold,
      transactionIndex: Number(transactionIndex.toString()),
      solPrice,
      vault: {
        address: vaultPda.toBase58(),
        balance: vaultBalance,
        balanceSOL: vaultBalance / 1e9,
        assets: calculateAssetWeights(assets),
        totalValue: calculateAssetWeights(assets).reduce((sum, asset) => sum + asset.value, 0)
      },
      members: members.map(({ key, permissions: { mask } }) => {
        return {
          address: key.toBase58(),
          permissions: parsePermissions(mask),
        }
      }), // TODO 过滤只有 Proposer 权限的用户
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const createTransfer = async (ctx) => {
  try {
    const { type, signedTransaction } = ctx.request.body

    let requiredParams = ['type', 'signedTransaction']
    if (type === 'token') requiredParams.push('tokenMint', 'decimals')
    const error = validateParams(ctx.request.body, requiredParams)
    if (error) return handleError(ctx, error)

    const signature = await sendTransaction(signedTransaction)
    const result = await connection.confirmTransaction(signature)

    ctx.body = {
      signature,
      result,
      success: true
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const getTransactions = async (ctx) => {
  try {
    const { page = 1, pageSize = 5 } = ctx.request.body

    // 多签账户信息
    const { threshold, transactionIndex } = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_PUBKEY)

    const transactions = []

    // 分页
    const totalPages = Math.ceil(transactionIndex / pageSize)
    const offset = (page - 1) * pageSize
    const startIndex = Math.max(1, transactionIndex - offset)
    const endIndex = Math.max(1, startIndex - pageSize + 1)

    for (let i = startIndex; i >= endIndex; i--) {
      // proposal 数据
      const [proposalPda] = multisig.getProposalPda({ multisigPda: MULTISIG_PUBKEY, transactionIndex: i, programId: SQUADS_V4_PUBKEY })
      const proposalAccount = await connection.getAccountInfo(proposalPda)
      const { approved, rejected, status: { timestamp, __kind: status } } = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0]

      // vaultTransaction 数据
      const [transactionPda] = multisig.getTransactionPda({ multisigPda: MULTISIG_PUBKEY, index: BigInt(i), programId: SQUADS_V4_PUBKEY })
      const transactionAccount = await connection.getAccountInfo(transactionPda)

      // 检查账户数据是否有效
      if (transactionAccount.data.length < 150) continue
      const { message, message: { instructions, accountKeys } } = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0]

      // 获取第一个指令的程序 ID
      const programId = accountKeys[instructions[0].programIdIndex]
      let transferData = null

      try {
        if (programId.equals(SystemProgram.programId)) {
          // System Program 转账 (SOL)
          console.log(`Transaction ${i}: System Program transfer`)
          transferData = await parseSystemTransfer(instructions[0], message)
        } else if (programId.equals(SPL_TOKEN_PUBKEY)) {
          // SPL Token Program 转账
          console.log(`Transaction ${i}: SPL Token transfer`)
          transferData = await parseSplTokenTransfer(instructions[0], message)
        } else {
          // 其他程序，跳过或使用默认解析
          console.log(`Transaction ${i}: Unknown program ${programId.toBase58()}, skipping`)
          continue
        }
      } catch (error) {
        console.error(`Failed to parse transaction ${i}:`, error.message)
        // 如果解析失败，跳过这个交易
        continue
      }

      transactions.push({
        transactionIndex: i,
        amount: transferData.amount,
        transactionType: 'transfer',
        transferToken: transferData.transferToken,
        from: transferData.from,
        to: transferData.to,
        authority: transferData.authority || null,
        mint: transferData.mint || null,
        decimals: transferData.decimals || 9,
        rawAmount: transferData.rawAmount || null,
        createdAt: new Date(Number(timestamp.toString()) * 1000).toISOString(),
        status,
        approvals: approved.length,
        threshold,
        canExecute: status === 'Approved',
        votes: [
          ...approved.map(member => ({ member: member.toBase58(), vote: 'Approve' })),
          ...rejected.map(member => ({ member: member.toBase58(), vote: 'Reject' }))
        ]
      })
    }

    ctx.body = {
      transactions,
      pagination: {
        page,
        pageSize,
        total: Number(transactionIndex),
        totalPages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

// 构建执行指令
export const buildExecuteInstruction = async (ctx) => {
  try {
    const requiredParams = ['multisigAddress', 'transactionIndex', 'executorPublicKey']
    const error = validateParams(ctx.request.body, requiredParams)
    if (error) return handleError(ctx, error)

    const { multisigAddress, transactionIndex, executorPublicKey } = ctx.request.body
    const multisigPda = new PublicKey(multisigAddress)
    const executorPubkey = new PublicKey(executorPublicKey)

    const instructionResult = await multisig.instructions.vaultTransactionExecute({
      connection,
      multisigPda,
      transactionIndex: BigInt(transactionIndex),
      member: executorPubkey,
      programId: SQUADS_V4_PUBKEY
    })

    if (!instructionResult) throw new Error('指令构建返回空结果')

    let instruction
    if (instructionResult.keys && instructionResult.programId && instructionResult.data !== undefined) {
      instruction = instructionResult
    } else if (instructionResult.instruction) {
      instruction = instructionResult.instruction
    } else if (Array.isArray(instructionResult) && instructionResult.length > 0) {
      instruction = instructionResult[0]
    } else {
      throw new Error('无法解析指令结果格式')
    }

    if (!instruction.keys || !instruction.programId || instruction.data === undefined) {
      throw new Error('指令格式不完整')
    }

    ctx.body = {
      instruction: {
        keys: instruction.keys.map(key => ({
          pubkey: key.pubkey.toBase58(),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: instruction.programId.toBase58(),
        data: Array.from(instruction.data)
      },
      lookupTableAccounts: []
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

// 交易操作（投票和执行）
export const handleTransactionAction = async (ctx) => {
  try {
    const { action } = ctx.params

    if (!['vote', 'execute'].includes(action)) {
      return handleError(ctx, '无效的操作类型')
    }

    let requiredParams = ['multisigAddress', 'transactionIndex', 'userPublicKey', 'signedTransaction']

    if (action === 'vote') {
      requiredParams.push('vote')
    }

    const error = validateParams(ctx.request.body, requiredParams)
    if (error) return handleError(ctx,  error)

    const { multisigAddress, transactionIndex, signedTransaction } = ctx.request.body

    if (action === 'execute') {
      // 执行前验证
      const MULTISIG_PUBKEY = new PublicKey(multisigAddress)

      const [proposalPda] = multisig.getProposalPda({
        multisigPda: MULTISIG_PUBKEY,
        transactionIndex: BigInt(transactionIndex),
        programId: SQUADS_V4_PUBKEY
      })

      const proposalAccount = await connection.getAccountInfo(proposalPda)
      if (!proposalAccount) {
        return handleError(ctx, `交易 #${transactionIndex} 不存在`)
      }

      const [transactionPda] = multisig.getTransactionPda({
        multisigPda: MULTISIG_PUBKEY,
        index: BigInt(transactionIndex),
        programId: SQUADS_V4_PUBKEY
      })

      const transactionAccount = await connection.getAccountInfo(transactionPda)
      if (!transactionAccount) {
        return handleError(ctx, `交易 #${transactionIndex} 没有对应的交易内容，无法执行`)
      }

      const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0]
      const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_PUBKEY)

      const approvals = proposalData.approved.length
      const threshold = multisigData.threshold

      if (approvals < threshold) {
        return handleError(ctx, `交易投票不足，需要 ${threshold} 票，当前只有 ${approvals} 票`)
      }

      if (proposalData.status.__kind === 'Executed') {
        return handleError(ctx, `交易 #${transactionIndex} 已经执行过了`)
      }

      if (proposalData.status.__kind === 'Cancelled') {
        return handleError(ctx, `交易 #${transactionIndex} 已被取消`)
      }

      const executorPubkey = new PublicKey(ctx.request.body.userPublicKey)
      const isMember = multisigData.members.some(member => member.key.equals(executorPubkey))
      if (!isMember) {
        return handleError(ctx, '执行者不是多签成员')
      }
    }

    const signature = await sendTransaction(signedTransaction)
    const result = await connection.confirmTransaction(signature)

    ctx.body = {
      signature,
      result,
      success: true,
      message: action === 'vote' ? '投票提交成功' : '交易执行成功'
    }
  } catch (error) {
    const actionName = ctx.params.action === 'vote' ? '投票' : '执行交易'
    handleError(ctx, error.message)
  }
}

// 取消交易
export const cancelTransaction = async (ctx) => {
  try {
    const requiredParams = ['multisigAddress', 'transactionIndex', 'userPublicKey', 'signedTransaction']
    const error = validateParams(ctx.request.body, requiredParams)
    if (error) return handleError(ctx, error)

    const { multisigAddress, transactionIndex, userPublicKey, signedTransaction } = ctx.request.body

    // 发送交易
    const signature = await sendTransaction(signedTransaction)

    // 等待确认
    await confirmTransactionWithTimeout(signature)

    ctx.body = {
      success: true,
      signature,
      message: '交易已取消'
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}
