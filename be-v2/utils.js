import axios from 'axios'
import * as multisig from '@sqds/multisig'
import { Connection, PublicKey, SystemProgram } from '@solana/web3.js'
import TOKENS from '../tokens.json' assert {type:'json'}
import { MULTISIG_PUBKEY, SQUADS_V4_PUBKEY, SPL_TOKEN_PUBKEY, SOLANA_RPC_URL, TX_TIMEOUT, TX_MAX_RETRIES, PERMISSION_MASKS } from './constants.js'

export const connection = new Connection(SOLANA_RPC_URL, 'confirmed')

export const handleError = (ctx, error) => {
  console.error(`error: ${error}`)
  ctx.status = 200
  ctx.body = { error }
}

export async function getPrice(chain) {
  // TODO 1: 假的
  let price = 0
  const tokenPrices = {
    'USDC': 1,
    'USDT': 1,
    'SOL': 100,
    'mSOL': 95,
    'BONK': 0.00001,
    'ETH': 2500,
    'BTC': 45000,
    'solusdc': 1,
    'solusdt': 1,
  }
  try {
    if (chain === 'SOL') {
      const { data } = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd', { timeout: 5000 })
      price = data?.solana?.usd || 0
    } else {
      price = tokenPrices[chain] || 0
    }
  } catch (error) {
    console.error(`get ${chain} price error: ${error.message}`)
  }
  return price
}

export async function getTokenAccounts(vaultPda) {
  const tokenAccounts = []
  try {
    const tokenAccountsResponse = await connection.getParsedTokenAccountsByOwner(vaultPda, { programId: SPL_TOKEN_PUBKEY})
    for (const { pubkey, account: { data: { parsed: { info: { mint, tokenAmount: { uiAmount, amount, decimals, uiAmountString }}}}}} of tokenAccountsResponse.value) {
      // 只包含有余额的 Token 账户
      if (uiAmount > 0) {
        const tokenPrice = await getPrice(getTokenName(mint))
        tokenAccounts.push({
          address: pubkey.toBase58(),
          mint: mint,
          name: getTokenName(mint),
          balance: parseInt(amount),
          decimals: decimals,
          uiAmount: uiAmount,
          uiAmountString: uiAmountString,
          price: tokenPrice,
          value: uiAmount * tokenPrice
        })
      }
    }
  } catch (error) {
    console.error(`get token account error:`, error.message)
  }
  return tokenAccounts
}
// ==============

// 工具函数
export const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key])
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null
}

export const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: 'confirmed',
      maxRetries: TX_MAX_RETRIES,
      ...options
    }
  )
  return signature
}

export const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, 'confirmed'),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ])
    return { confirmed: true, result }
  } catch (error) {
    return { confirmed: false, error: error.message }
  }
}

export async function parseSystemTransfer(instruction, message) {
  const accountIndexes = Object.values(instruction.accountIndexes)
  const dataBuffer = Buffer.from(Object.values(instruction.data))
  console.log('System Transfer - from:', message.accountKeys[accountIndexes[0]].toBase58())
  console.log('System Transfer - to:', message.accountKeys[accountIndexes[1]].toBase58())
  console.log('System Transfer - amount:', dataBuffer.readBigUInt64LE(4))
  const rawAmount = dataBuffer.readBigUInt64LE(4)
  return {
    from: message.accountKeys[accountIndexes[0]].toBase58(),
    to: message.accountKeys[accountIndexes[1]].toBase58(),
    amount: Number(rawAmount) / 1e9,
    transferToken: 'SOL',
    mint: null,
    decimals: 9,
    rawAmount: rawAmount.toString()
  }
}

export async function parseSplTokenTransfer(instruction, message) {
  const accountIndexes = Object.values(instruction.accountIndexes)
  const dataBuffer = Buffer.from(Object.values(instruction.data))

  // SPL Token Transfer 指令格式：
  // - 指令编号: 1 字节 (值为 3)
  // - amount: 8 字节 (u64, 小端序)

  if (dataBuffer.length < 9) {
    throw new Error('SPL Token transfer instruction data too short')
  }

  const instructionType = dataBuffer.readUInt8(0)
  if (instructionType !== 3) {
    throw new Error(`Expected SPL Token Transfer instruction (3), got ${instructionType}`)
  }

  const amount = dataBuffer.readBigUInt64LE(1) // 从偏移量 1 开始读取 amount

  // SPL Token Transfer 账户布局：
  // 0: [writable] 源 token 账户
  // 1: [writable] 目标 token 账户
  // 2: [signer] 源账户的所有者/委托人

  const sourceTokenAccount = message.accountKeys[accountIndexes[0]].toBase58()
  const destTokenAccount = message.accountKeys[accountIndexes[1]].toBase58()
  const authority = message.accountKeys[accountIndexes[2]].toBase58()

  console.log('SPL Token Transfer - source token account:', sourceTokenAccount)
  console.log('SPL Token Transfer - dest token account:', destTokenAccount)
  console.log('SPL Token Transfer - authority:', authority)
  console.log('SPL Token Transfer - amount:', amount.toString())

  // 尝试获取 token mint 信息（需要查询账户信息）
  let mint = null
  let decimals = 6 // 默认值
  let tokenName = 'Unknown Token'

  try {
    // 获取源 token 账户信息来确定 mint
    const sourceAccountInfo = await connection.getParsedAccountInfo(new PublicKey(sourceTokenAccount))
    if (sourceAccountInfo.value && sourceAccountInfo.value.data.parsed) {
      mint = sourceAccountInfo.value.data.parsed.info.mint
      decimals = sourceAccountInfo.value.data.parsed.info.tokenAmount.decimals
      tokenName = getTokenName(mint) || 'Unknown Token'
    }
  } catch (error) {
    console.error('Failed to get token account info:', error.message)
  }

  return {
    from: sourceTokenAccount,
    to: destTokenAccount,
    authority,
    amount: Number(amount) / Math.pow(10, decimals),
    transferToken: tokenName,
    mint,
    decimals,
    rawAmount: amount.toString()
  }
}

const TOKEN_NAME_MAP = {}
for (const [symbol, { asset_id }] of Object.entries(TOKENS)) {
  TOKEN_NAME_MAP[asset_id] = symbol
}

export const getTokenName = mint => TOKEN_NAME_MAP[mint]

export function parsePermissions(mask) {
  const permissions = []
  if ((mask & PERMISSION_MASKS.PROPOSER) !== 0) permissions.push('Proposer')
  if ((mask & PERMISSION_MASKS.VOTER) !== 0) permissions.push('Voter')
  if ((mask & PERMISSION_MASKS.EXECUTOR) !== 0) permissions.push('Executor')
  return permissions
}

export function calculateAssetWeights(assets) {
  const totalValue = assets.reduce((sum, asset) => sum + asset.value, 0)

  return assets.map(asset => ({
    ...asset,
    weight: totalValue > 0 ? asset.value / totalValue : 0
  }))
}
